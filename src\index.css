/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;

}

body {
  font-family: Arial, sans-serif;
  line-height: 1.5;
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
}

/* Hide scrollbar for Chrome, Safari and Opera */
body::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
body {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* App Layout */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main {
  flex: 1;
  /* Height of navbar */
}

/* .team-profiles-section {
  margin-top: 500px;
  width: 100%;
    max-Width: 1200px;
    margin: 80px auto 100px;
    padding: 0 20px;
} */

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.navbar {
  background-color: #007AFF;
  padding: 1rem 0;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: opacity 0.2s;
}

.nav-link:hover {
  opacity: 0.8;
}

/* Mobile Navigation */
.mobile-menu-btn {
  display: none;
}

.menu-icon {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
}

.mobile-nav {
  background-color: #007AFF;
  padding: 1rem;
}

.mobile-nav-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobile-nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  transition: opacity 0.2s;
}

.mobile-nav-link:hover {
  opacity: 0.8;
}

/* Hero Section */
.hero {
  background: linear-gradient(to right, #007AFF, #6C63FF);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}


.hero p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
}

/* Button styles */
.button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-weight: bold;
  cursor: pointer;
  transition: transform 0.2s;
}

.button:hover {
  transform: scale(1.05);
}



.button-primary {
  background-color: #007AFF;
  color: white;
  border: none;
}



.button-white {
  background-color: white;
  color: #007AFF;
}

/* Section styles */
.section {
  padding: 4rem 0;
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 2rem;
}

/* Grid layout */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Contact Page Styles */
.contact-page {
  padding-top: 60px;
}

.contact-hero {
  background: linear-gradient(135deg, #007AFF, #6C63FF);
  padding: 6rem 0;
  text-align: center;
  color: white;
}

.contact-hero h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
}

.contact-hero p {
  font-size: 1.25rem;
  max-width: 600px;
  margin: 0 auto;
}

.contact-form-section {
  padding: 5rem 0;
  background-color: #f8f9fa;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 4rem;
  align-items: start;
}

.contact-info {
  background-color: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.contact-info h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: #1a1a1a;
}

.info-items {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.info-item i {
  font-size: 1.5rem;
  color: #007AFF;
}

.info-item h3 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: #1a1a1a;
}

.info-item p {
  color: #666;
  line-height: 1.6;
}

.contact-form {
  background-color: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #1a1a1a;
  font-weight: 500;
}


.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007AFF;
}

.map-section {
  padding: 5rem 0;
}

.map-container {
  height: 400px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.map-placeholder {
  height: 100%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
}

/* Responsive design for contact page */
@media (max-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-hero {
    padding: 4rem 0;
  }

  .contact-hero h1 {
    font-size: 2.5rem;
  }

  .contact-form {
    padding: 2rem;
  }
}

/* About Page Styles */
.about-page {
  min-height: 100vh;
  background: linear-gradient(to bottom, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.about-hero {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 2rem;
}

.about-hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.about-hero h1 {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero-description {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.about-content {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.mission-vision-section {
  margin-bottom: 4rem;
}

.mission-vision-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.mission-card,
.vision-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.mission-card h2,
.vision-card h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.values-section {
  text-align: center;
  margin-bottom: 4rem;
}

.values-section h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: var(--primary);
}

.values-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.value-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.value-card:hover {
  transform: translateY(-5px);
}

.value-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: inline-block;
}

.value-card h3 {
  font-size: 1.4rem;
  margin-bottom: 1rem;
  color: var(--primary);
}



.diversity-section {
  max-width: 800px;
  margin: 0 auto 4rem;
  text-align: center;
}

.diversity-section h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: var(--primary);
}

.diversity-content {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  text-align: left;
}

.diversity-content p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

/* Services Page Styles */
.services-page {
  min-height: 100vh;
  background: linear-gradient(to bottom, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.services-hero {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 2rem;
}

.services-hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.services-hero h1 {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-top: 1rem;
}

.services-grid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.service-section {
  margin-bottom: 4rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.service-section h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--primary);
}

.service-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: var(--text-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem;
  border-radius: 0.75rem;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card h3 {
  font-size: 1.2rem;
  margin-bottom: 0.75rem;
  color: var(--primary);
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.5;
}

@media (max-width: 768px) {

  .about-hero h1,
  .services-hero h1 {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.25rem;
  }

  .mission-vision-grid,
  .values-grid,
  .features-grid {
    grid-template-columns: 1fr;
  }

  .service-section {
    padding: 1.5rem;
  }
}

@media (max-width: 1024px) {
  .values-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .about-hero h1 {
    font-size: 3rem;
  }

  .mission-vision-grid,
  .team-grid,
  .research-grid {
    grid-template-columns: 1fr;
  }

  .values-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .member-image {
    width: 160px;
    height: 160px;
  }
}

@media (max-width: 480px) {
  .values-grid {
    grid-template-columns: 1fr;
  }

  .about-hero h1 {
    font-size: 2.5rem;
  }

  .about-hero p {
    font-size: 1.25rem;
  }
}

/* Home Page Styles */

.home-page {
  padding-top:11px;
 
}

.hero-section {
  color: white;
  overflow: hidden;
  height:500px;
  max-width: calc(100vw - 40px);
  width: 100%;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 40px;
  padding-top: 120px;
  padding-left: 60px;
  margin-bottom: 30px;
  box-sizing: border-box;
}

.zero-section {
  background: white;
  padding: 8rem 0;
  text-align: center;
  color: white;
  position: relative;
  overflow: hidden;
  border-radius: 20px;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/path-to-pattern.svg') repeat;
  opacity: 0.1;
  pointer-events: none;
}

.hero-section h1 {
  font-size: 4.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
}

.hero-tagline {
  font-size: 20px;
  max-width: 800px;
  opacity: 0.9;
  margin-bottom: 20px;
}

.welcome-section {
  color: white;
  overflow: hidden;
  height:500px;
  max-width: calc(100vw - 40px);
  width: 100%;
  margin-left: 20px;
  margin-right: 20px;
  border-radius: 40px;
  padding-top: 100px;
  padding-left: 60px;
  box-sizing: border-box;
}

.welcome-section h2 {
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 2rem;
}

.welcome-section p {
  max-width: 800px;
  margin: 0 auto 1.5rem;
  font-size: 1.2rem;
  line-height: 1.8;
  color: #4a5568;
}

.services-preview {
  margin-top: 20px;
  padding: 4rem 0;
  background: white;
}

.services-preview h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 4rem;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.service-card {
  background: #040404;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
}


.service-card p {
  color: #4a5568;
  line-height: 1.6;
}

.explore-link {
  /* display: block; */
  text-align: center;
  /* margin-top: 3rem; */
  color: #007AFF;
  font-size: 1.2rem;
  font-weight: 500;
  text-decoration: none;
  color:black;
  transition: color 0.3s ease;
}

.explore-link:hover {
  color: #0056b3;
}

.testimonials {
  margin-left: 20px;
  margin-right: 20px;
  padding: 6rem 0;
  background: #e8e8e8;
  border-radius: 30px;
}

.testimonials h2 {
  text-align: center;
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 4rem;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.testimonial-card {
  background:#FF6B00;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stars {
  margin-bottom: 1rem;
}

.testimonial-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color:white;
  margin-bottom: 1.5rem;
}

.testimonial-author {
  font-weight: 600;
  color: #2d3748;
}

.testimonial-position {
  color: rgb(220, 218, 218);
  font-size: 0.9rem;
}

.achievements {
  margin-top: 30px;
  position: relative;
          background: white;
          overflow: hidden;
          width: 100%;
          padding: 64px 0;
}

.achievements h2 {
  text-align: center;
  color: #212121;
  font-size: 40px;
  font-weight: 700;
  margin-bottom: 48px;
}

.achievements-container {
  margin: 0 auto;
  padding: 0 20px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 32px;
  
}

.achievement-icon {
  width: 28px;
  height: 28px;
  background: #D9D9D9;
  flex-shrink: 0;
}

.achievement-text {
  
  font-family: 'Instrument Sans', sans-serif;
  font-weight: 700;
  line-height: 36px;
}

.cta-section {
  margin-bottom: 50px;
  padding: 20px;
  text-align: center;
}

.cta-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
}

.cta-section p {
  font-size: 1.2rem;
  max-width: 600px;
  margin: 0 auto 3rem;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-button {
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: transform 0.3s ease;
  text-decoration: none;
}

.cta-button:hover {
  transform: translateY(-2px);
}

.cta-button:not(.secondary) {
  background: white;
  color: #007AFF;
}

.cta-button.secondary {
  background: transparent;
  border: 2px solid white;
  color: white;
}

/* Enhanced Mobile Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    margin-left: 10px;
    margin-right: 10px;
    max-width: calc(100vw - 20px);
    padding: 60px 20px 40px 20px;
    height: auto;
    min-height: 400px;
  }

  .welcome-section {
    margin-left: 10px;
    margin-right: 10px;
    max-width: calc(100vw - 20px);
    padding: 30px 20px;
  }

  .hero-section h1 {
    font-size: 2.5rem;
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  .hero-tagline {
    font-size: 1.2rem;
    line-height: 1.4;
    margin-bottom: 2rem;
  }

  .services-grid,
  .testimonials-grid,
  .achievements-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 1rem;
  }

  .cta-buttons {
    flex-direction: column;
    padding: 0 1rem;
    gap: 1rem;
  }

  .cta-button {
    width: 100%;
    padding: 1rem 2rem;
    font-size: 1rem;
    text-align: center;
  }

  /* Mobile-specific improvements */
  .service-card {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .service-card h3 {
    font-size: 1.25rem;
  }

  .service-card p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .hero-section {
    padding: 40px 15px 30px 15px;
    margin-left: 5px;
    margin-right: 5px;
    max-width: calc(100vw - 10px);
  }

  .hero-section h1 {
    font-size: 2rem;
    line-height: 1.1;
  }

  .hero-tagline {
    font-size: 1rem;
    line-height: 1.3;
  }

  .service-card {
    padding: 1rem;
  }

  .cta-button {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Touch-friendly interactions */
  button, .cta-button, .service-card {
    min-height: 44px; /* Apple's recommended touch target size */
    touch-action: manipulation; /* Prevents zoom on double-tap */
  }

  /* Improved text readability on mobile */
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Better scrolling performance */
  * {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent horizontal scroll */
  body, html {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Mobile-optimized animations */
  .service-card, .product-card {
    transition: transform 0.2s ease-out;
  }

  /* Improved form inputs for mobile */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid #e1e5e9;
    width: 100%;
    box-sizing: border-box;
  }

  input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #0A2647;
    box-shadow: 0 0 0 3px rgba(10, 38, 71, 0.1);
  }

  /* Mobile navigation improvements */
  .navbar {
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }

  /* Better spacing for mobile content */
  .container, .section {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile-optimized grid layouts */
  .grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Improved button styles for mobile */
  .btn, .cta-button {
    display: block;
    width: 100%;
    text-align: center;
    padding: 1rem 2rem;
    margin-bottom: 1rem;
    border-radius: 12px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.2s ease;
  }

  .btn:active, .cta-button:active {
    transform: scale(0.98);
  }
}

/* Footer */
.footer {
  background-color: var(--neuro-blue);
  color: white;
  padding: 4rem 0 2rem;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-company {
  grid-column: span 2;
}

.footer-company h3 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
}

.footer-company h3 span {
  color: var(--neuro-orange);
}

.footer-company p {
  color: #e0e0e0;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  max-width: 400px;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  color: white;
  transition: color 0.3s ease;
  padding: 0.5rem;
}

.social-links a:hover {
  color: var(--neuro-orange);
}

.footer-links,
.footer-services,
.footer-contact {
  display: flex;
  flex-direction: column;
}

.footer h4 {
  color: var(--neuro-orange);
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.footer ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer ul li {
  margin-bottom: 0.8rem;
}

.footer ul li a {
  color: #e0e0e0;
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.95rem;
}

.footer ul li a:hover {
  color: var(--neuro-orange);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: #e0e0e0;
  font-size: 0.9rem;
}

.footer-legal {
  display: flex;
  gap: 2rem;
}

.footer-legal a {
  color: #e0e0e0;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-legal a:hover {
  color: var(--neuro-orange);
}

@media (max-width: 1024px) {
  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-company {
    grid-column: span 2;
  }
}

@media (max-width: 640px) {
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-company {
    grid-column: span 1;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .footer-legal {
    justify-content: center;
  }
}

/* Navbar Styles */
:root {
  --neuro-blue: #0A2647;
  --neuro-orange: #FF6B00;
}

.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 2rem;
  transition: all 0.3s ease;
  background: var(--neuro-blue);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar.scrolled {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-logo {
  font-size: 1.8rem;
  font-weight: bold;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navbar-logo span:first-child {
  color: var(--neuro-orange);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-menu.mobile {
  display: none;
}

.nav-link {
  color: white;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
  opacity: 0.9;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--neuro-orange);
  transition: width 0.3s ease;
}

.nav-link:hover {
  opacity: 1;
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-link.active {
  color: var(--neuro-orange);
  opacity: 1;
}

/* Footer Styles */
.footer {
  background: var(--neuro-blue);
  padding: 4rem 0 2rem;
  color: white;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
      transparent 0%,
      var(--neuro-orange) 50%,
      transparent 100%);
  opacity: 0.2;
}

.footer-company h3 {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: white;
}

.footer-company h3 span:first-child {
  color: var(--neuro-orange);
}

.footer-company p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.social-links a {
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease, transform 0.3s ease;
}

.social-links a:hover {
  color: var(--neuro-orange);
  transform: translateY(-2px);
}

.footer h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--neuro-orange);
}

.footer ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer ul li a:hover {
  color: var(--neuro-orange);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  margin-top: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgba(255, 255, 255, 0.8);
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-legal a:hover {
  color: var(--neuro-orange);
}

@media (max-width: 768px) {
  .footer {
    padding: 3rem 0 1.5rem;
  }

  .footer-grid {
    gap: 2rem;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .footer-legal {
    justify-content: center;
  }
}

/* Products Page Styles */
.products-page {
  min-height: 100vh;
  background: linear-gradient(to bottom, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  padding-top: 80px;
}

.products-hero {
  min-height: 40vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 2rem;
}

.products-hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.products-hero h1 {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.products-grid {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.product-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: inline-block;
}

.product-card h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: var(--primary);
}

.product-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.product-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.product-features li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
}

.product-features li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--primary);
}

.products-cta {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.05);
  margin-top: 4rem;
}

.products-cta h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: var(--primary);
}

.products-cta p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.cta-button {
  padding: 0.75rem 2rem;
  border-radius: 2rem;
  font-weight: 500;
  text-decoration: none;
  transition: transform 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
}

.cta-button.primary {
  background: var(--primary);
  color: white;
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--primary);
  border: 2px solid var(--primary);
}

@media (max-width: 768px) {
  .navbar {
    padding: 1rem;
  }

  .nav-menu.desktop {
    display: none;
  }

  .mobile-menu-button {
    display: flex;
  }

  .nav-menu.mobile {
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(var(--bg-primary-rgb), 0.95);
    backdrop-filter: blur(10px);
    padding: 5rem 2rem 2rem;
    align-items: center;
  }

  .products-hero h1 {
    font-size: 2.5rem;
  }

  .products-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .cta-button {
    width: 100%;
    text-align: center;
  }
}

/* Products & Services Pages */
.products-page,
.services-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.products-hero,
.services-hero {
  background-color: var(--neuro-blue);
  color: white;
  padding: 6rem 0 4rem;
  text-align: center;
}

.products-hero-content,
.services-hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.products-hero h1,
.services-hero h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.products-hero p,
.services-hero p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
}

.products-grid,
.services-grid {
  max-width: 1200px;
  margin: -3rem auto 4rem;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  position: relative;
}

.product-card,
.service-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.product-card:hover,
.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.product-icon,
.service-icon {
  font-size: 2rem;
  color: var(--neuro-orange);
  margin-bottom: 1.5rem;
}

.product-card h2,
.service-card h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--neuro-blue);
}

.product-description,
.service-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.product-features,
.service-features {
  list-style: none;
  padding: 0;
  margin: 0;
  flex-grow: 1;
}

.product-features li,
.service-features li {
  color: #444;
  margin-bottom: 0.8rem;
  padding-left: 1.5rem;
  position: relative;
}

.product-features li::before,
.service-features li::before {
  content: "•";
  color: var(--neuro-orange);
  position: absolute;
  left: 0;
}

.products-cta,
.services-cta {
  background-color: white;
  padding: 4rem 2rem;
  text-align: center;
  margin-top: 4rem;
}

.products-cta h2,
.services-cta h2 {
  font-size: 2.5rem;
  color: var(--neuro-blue);
  margin-bottom: 1.5rem;
}

.products-cta p,
.services-cta p {
  color: #666;
  max-width: 600px;
  margin: 0 auto 2rem;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.cta-button {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.cta-button.primary {
  background-color: var(--neuro-orange);
  color: white;
}

.cta-button.primary:hover {
  background-color: #e65c00;
  transform: translateY(-2px);
}

.cta-button.secondary {
  background-color: transparent;
  color: var(--neuro-blue);
  border: 2px solid var(--neuro-blue);
}

.cta-button.secondary:hover {
  background-color: var(--neuro-blue);
  color: white;
  transform: translateY(-2px);
}

@media (max-width: 768px) {

  .products-hero h1,
  .services-hero h1 {
    font-size: 2.5rem;
  }

  .products-grid,
  .services-grid {
    grid-template-columns: 1fr;
    margin-top: -2rem;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .products-cta h2,
  .services-cta h2 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {

  .products-hero h1,
  .services-hero h1 {
    font-size: 2rem;
  }

  .product-card,
  .service-card {
    padding: 1.5rem;
  }
}

/* about us page */
/* .about_us_heading {
  align-Self: stretch;
    text-Align: center;
    color: 'black';
    font-Size: '48px';
    font-Family: 'Instrument Sans, sans-serif';
    font-Weight: '700';
    line-Height: '72px';
} */

.explore-all-services {
  background: #FFCEAA;
}

.explore-all-services:hover {
  background: #d5844b;  
}

/* CSS for responsiveness for about us */
@media screen and (max-width: 1200px) {
  .team-profiles-section {
    margin-left: auto;
    margin-right: auto;
  }
}

@media screen and (max-width: 768px) {
  .about-page {
    overflow-x: hidden;
  }
  
  .zero-section {
    margin-left: 0 !important;
    margin: 0 auto !important;
    max-width: 90% !important;
    padding: 40px 15px !important;
  }
  
  .zero-section h1 {
    font-size: 36px !important;
    line-height: 48px !important;
  }
  
  .zero-section p {
    font-size: 16px !important;
    line-height: 28px !important;
  }
  
  .profile-container {
    flex-direction: column !important;
    text-align: center;
  }
  
  .profile-container > div:last-child {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .cta-section h2 {
    font-size: 28px !important;
  }
  
  .cta-section p {
    font-size: 16px !important;
  }
}

@media screen and (max-width: 480px) {
  .zero-section h1 {
    font-size: 28px !important;
    line-height: 42px !important;
  }
  
  .new-design-section {
    padding: 20px 10px !important;
  }
}