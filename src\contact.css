.contact-page {
    font-family: 'Instrument Sans', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    color: #2A2A2A;
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  h1, h2, h3 {
    margin: 0;
    font-weight: 700;
  }
  
  h1 {
    font-size: 48px;
    line-height: 1.2;
  }
  
  h2 {
    font-size: 32px;
    margin-bottom: 20px;
    color: #0A2647;
  }
  
  h3 {
    font-size: 20px;
    margin-bottom: 8px;
    color: #0A2647;
  }
  
  p {
    margin: 0 0 15px;
    line-height: 1.6;
  }
  
  .text-link {
    color: #1C69C4;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
  }
  
  .text-link:hover {
    color: #0A2647;
    text-decoration: underline;
  }
  
  /* Hero Section */
  .contact-hero {
    position: relative;
    background-image: url('/path/to/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    padding: 120px 0;
    text-align: center;
    color: white;
  }
  
  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(10, 38, 71, 0.8), rgba(10, 38, 71, 0.9));
  }
  
  .contact-hero-content {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
  }
  
  .contact-hero p {
    font-size: 20px;
    margin-top: 20px;
    opacity: 0.9;
  }
  
  /* Contact Form Section */
  .contact-form-section {
    padding: 80px 0;
    background-color: #f8f9fa;
  }
  
  .contact-grid {
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: 60px;
  }
  
  /* Contact Info */
  .contact-info {
    padding: 30px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  }
  
  .contact-subtitle {
    font-size: 18px;
    color: #555;
    margin-bottom: 30px;
  }
  
  .info-items {
    margin-top: 40px;
    display: flex;
    flex-direction: column;
    gap: 30px;
  }
  
  .info-item {
    display: flex;
    align-items: flex-start;
  }
  
  .info-icon-wrapper {
    width: 50px;
    height: 50px;
    background-color: rgba(28, 105, 196, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
  }
  
  .info-icon {
    font-size: 22px;
    color: #1C69C4;
  }
  
  .info-content {
    flex: 1;
  }
  
  .info-note {
    font-size: 14px;
    color: #777;
    margin-top: 4px;
  }
  
  .email-link {
    color: #1C69C4;
    font-weight: 500;
  }
  
  .cta-box {
    margin-top: 50px;
    padding: 25px;
    background-color: rgba(28, 105, 196, 0.05);
    border-left: 4px solid #1C69C4;
    border-radius: 6px;
  }
  
  /* Contact Form */
  .contact-form {
    padding: 40px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  }
  
  .form-header {
    margin-bottom: 30px;
  }
  
  .form-header p {
    color: #555;
    font-size: 18px;
  }
  
  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }
  
  .form-group {
    margin-bottom: 25px;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
  }
  
  .form-group input,
  .form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    font-family: inherit;
  }
  
  .form-group input:focus,
  .form-group textarea:focus {
    border-color: #1C69C4;
    outline: none;
    box-shadow: 0 0 0 3px rgba(28, 105, 196, 0.1);
  }
  
  .form-group input::placeholder,
  .form-group textarea::placeholder {
    color: #aaa;
  }
  
  .form-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
  }
  
  .privacy-note {
    font-size: 14px;
    color: #777;
  }
  
  .button {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .button-primary {
    background-color: #0A2647;
    color: white;
    box-shadow: 0 4px 12px rgba(10, 38, 71, 0.2);
  }
  
  .button-primary:hover {
    background-color: #134886;
  }
  
  /* Map Section */
  .map-section {
    padding: 60px 0 100px;
  }
  
  .map-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  }
  
  .map-placeholder {
    position: relative;
    width: 100%;
    height: 400px;
    background-color: #e9ecef;
    background-image: url('/path/to/map-placeholder.jpg');
    background-size: cover;
    background-position: center;
  }
  
  .map-overlay {
    position: absolute;
    bottom: 30px;
    left: 30px;
    background-color: white;
    padding: 20px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 300px;
  }
  
  .map-button {
    display: inline-block;
    background-color: #1C69C4;
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    margin-top: 10px;
    transition: background-color 0.3s ease;
  }
  
  .map-button:hover {
    background-color: #0A2647;
  }
  
  /* Responsive Styles */
  @media (max-width: 992px) {
    .contact-grid {
      grid-template-columns: 1fr;
      gap: 40px;
    }
    
    .contact-hero {
      padding: 100px 0;
    }
    
    h1 {
      font-size: 40px;
    }
  }
  
  @media (max-width: 768px) {
    .form-row {
      grid-template-columns: 1fr;
    }
    
    .contact-hero {
      padding: 80px 0;
    }
    
    .contact-form, .contact-info {
      padding: 25px;
    }
    
    .form-footer {
      flex-direction: column;
      gap: 20px;
    }
    
    .map-overlay {
      left: 20px;
      bottom: 20px;
    }
  }