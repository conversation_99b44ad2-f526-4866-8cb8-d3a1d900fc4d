{"name": "neurostack-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.0", "@tanstack/react-query": "^5.20.0", "axios": "^1.6.7", "framer-motion": "^11.0.3", "react-helmet-async": "^2.0.5", "react-icons": "^5.0.1", "@headlessui/react": "^1.7.18"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "vite": "^5.0.12"}}